{"name": "@scroll-tech/contracts", "description": "A library for interacting with Scroll contracts.", "version": "2.0.1", "repository": {"type": "git", "url": "https://github.com/scroll-tech/scroll.git"}, "files": ["L1", "L2", "interfaces", "libraries", "misc", "mocks"], "keywords": ["solidity", "ethereum", "smart", "contracts", "layer2", "l2", "scroll", "zkevm", "zkp", "bridge", "erc20", "erc712", "erc1155"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/scroll-tech/scroll-contracts/issues"}, "homepage": "https://scroll.io/"}