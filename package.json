{"name": "scroll-contracts", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"test:hardhat": "npx hardhat test", "test:forge": "forge test -vvv --evm-version cancun", "test": "yarn test:hardhat && yarn test:forge", "solhint": "./node_modules/.bin/solhint -f table 'src/**/*.sol'", "lint:sol": "./node_modules/.bin/prettier --write 'src/**/*.sol'", "lint:ts": "./node_modules/.bin/prettier --write 'hardhat-test/**/*.ts' 'scripts/**/*.ts' *.ts", "lint": "yarn lint:ts && yarn lint:sol", "coverage": "hardhat coverage", "coverage:forge": "forge coverage", "prepare": "husky install .husky"}, "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.6", "@nomicfoundation/hardhat-ethers": "^3.0.5", "@nomicfoundation/hardhat-verify": "^2.0.5", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/chai": "^4.2.21", "@types/edit-json-file": "^1.7.0", "@types/mocha": "^9.0.0", "@types/node": "^20.11.27", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "chai": "^4.2.0", "circom": "^0.5.46", "circomlib": "^0.5.0", "dotenv": "^10.0.0", "edit-json-file": "^1.7.0", "eslint": "^8.57.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.23.4", "eslint-plugin-n": "^16.6.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-promise": "^6.1.1", "ethereum-waffle": "^3.0.0", "ethers": "^6.11.1", "hardhat": "^2.22.18", "hardhat-gas-reporter": "^1.0.4", "husky": "^8.0.1", "lint-staged": "^13.0.3", "lodash": "^4.17.21", "prettier": "^2.3.2", "prettier-plugin-solidity": "^1.0.0-beta.13", "solhint": "^3.3.6", "solidity-coverage": "^0.8.11", "squirrelly": "8.0.8", "toml": "^3.0.0", "ts-node": "^10.1.0", "typechain": "^8.3.2", "typescript": "^5.4.2"}, "dependencies": {"@openzeppelin/contracts": "^v4.9.3", "@openzeppelin/contracts-upgradeable": "^v4.9.3"}, "lint-staged": {"*.{js,ts}": "npx eslint --cache --fix", "!(docs/apis/*).md": "prettier --ignore-unknown --write", "*.sol": "prettier --ignore-unknown --write"}, "engines": {"node": ">=10.4.0"}}