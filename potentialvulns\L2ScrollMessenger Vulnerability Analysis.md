# L2ScrollMessenger Vulnerability Analysis

## Vulnerability Verification

### Code Analysis of `_executeMessage` Function

The vulnerability centers around the `_executeMessage` function in `L2ScrollMessenger.sol`. Let's examine the relevant code:

```solidity
function _executeMessage(
    address _from,
    address _to,
    uint256 _value,
    bytes memory _message,
    bytes32 _xDomainCalldataHash
) internal {
    // @note check more `_to` address to avoid attack in the future when we add more gateways.
    require(_to != messageQueue, "Forbid to call message queue");
    _validateTargetAddress(_to);

    // @note This usually will never happen, just in case.
    require(_from != xDomainMessageSender, "Invalid message sender");

    xDomainMessageSender = _from;
    // solhint-disable-next-line avoid-low-level-calls
    // no reentrancy risk, only alias(l1ScrollMessenger) can call relayMessage.
    // slither-disable-next-line reentrancy-eth
    (bool success, ) = _to.call{value: _value}(_message);
    // reset value to refund gas.
    xDomainMessageSender = ScrollConstants.DEFAULT_XDOMAIN_MESSAGE_SENDER;

    if (success) {
        isL1MessageExecuted[_xDomainCalldataHash] = true;
        emit RelayedMessage(_xDomainCalldataHash);
    } else {
        emit FailedRelayedMessage(_xDomainCalldataHash);
    }
}
```

### Vulnerability Confirmation

The reported vulnerability is **confirmed**. The critical issue is in the order of operations:

1. The function makes an external call to the target contract: `(bool success, ) = _to.call{value: _value}(_message);`
2. Only after the external call completes successfully does it set the execution flag: `isL1MessageExecuted[_xDomainCalldataHash] = true;`

This creates a vulnerability where if the external call succeeds but consumes enough gas to cause the subsequent state update to fail due to out-of-gas, the message's effects will be committed on the target contract, but `isL1MessageExecuted[_xDomainCalldataHash]` will remain `false`.

## Exploit Path Mapping

### Prerequisites for Exploitation

1. An attacker needs to send a cross-domain message from L1 to L2 via the L1ScrollMessenger
2. The target L2 contract must not be idempotent (i.e., executing the same operation twice would lead to unintended state changes)
3. The attacker must carefully craft the gas limit to ensure:
   - Enough gas for the target contract call to succeed
   - Not enough remaining gas for the subsequent state update operation

### Step-by-Step Exploit Path

1. **Initial Message Execution**:
   - Attacker sends a message from L1 to L2 via `L1ScrollMessenger.sendMessage()`
   - Message is relayed to L2 and `L2ScrollMessenger.relayMessage()` is called
   - Inside `_executeMessage()`, the external call to the target contract succeeds
   - The gas remaining is insufficient for the `SSTORE` operation to set `isL1MessageExecuted[_xDomainCalldataHash] = true`
   - The transaction reverts at this point due to out-of-gas, but the target contract's state changes remain committed

2. **Message Replay**:
   - Since `isL1MessageExecuted[_xDomainCalldataHash]` remains `false`, the message is eligible for replay
   - Attacker calls `L1ScrollMessenger.replayMessage()` with the same parameters
   - The message is relayed to L2 again and `L2ScrollMessenger.relayMessage()` is called
   - The check `require(!isL1MessageExecuted[_xDomainCalldataHash], "Message was already successfully executed")` passes
   - The target contract call is executed a second time, duplicating its effects

### Gas Consumption Analysis

The vulnerability relies on precise gas manipulation:

1. The EVM executes the external call first, which commits its state changes
2. When the external call returns, there must be insufficient gas for the subsequent `SSTORE` operation
3. The `SSTORE` operation (setting `isL1MessageExecuted[_xDomainCalldataHash] = true`) costs:
   - 20,000 gas for a 0→1 transition (first-time storage write)
   - Plus some gas for the hash calculation and memory operations

An attacker would need to calculate the gas required for the target contract execution plus the gas needed for operations before the external call, then provide just enough gas to complete the external call but not enough for the subsequent state update.

## Impact Assessment

The impact of this vulnerability is **High** because:

1. It allows double execution of L1→L2 messages, which can lead to:
   - Double minting of tokens
   - Double transfers of funds
   - Duplicated state changes in any non-idempotent contract

2. The vulnerability affects core cross-domain messaging infrastructure, potentially impacting all contracts that rely on L1→L2 messaging and do not implement their own idempotency checks.

3. The exploit does not require special permissions - any user who can send cross-domain messages can potentially exploit this vulnerability if they can precisely control gas usage.
