name: Build and publish scroll-alpine image
# This workflow aims to build images from the feat-deterministic-deployment branch

on:
  workflow_dispatch:

jobs:
  build:
    name: <PERSON><PERSON>, <PERSON>uild, Publish
    runs-on: ubuntu-latest
    permissions: {}

    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          submodules: true
          persist-credentials: false

      - name: Update submodules recursively
        run: git submodule update --init --recursive

      - name: Set up QEMU
        uses: docker/setup-qemu-action@29109295f81e9208d7d86ff1c6c12d2833863392 # v3.6.0

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: '21'

      - name: Install dependencies
        run: npm install

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@b5ca514318bd6ebac0fb2aedd5d36ec1b5c232a2 # v3.10.0
        with:
          cache-binary: false

      - name: Login to Dockerhub
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 #v3.4.0
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build deploy image
        id: build_deploy_image
        env:
          REPOSITORY: scrolltech/scroll-stack-contracts
        uses: docker/build-push-action@14487ce63c7a62a4a324b0bfb37086795e31c6c1 # v6.16.0
        with:
          platforms: linux/amd64,linux/arm64
          push: true
          context: .
          file: docker/Dockerfile.deploy
          tags: |
            ${{ env.REPOSITORY }}:deploy-${{ github.sha }}

      - name: Build gen image
        id: build_gen_image
        env:
          REPOSITORY: scrolltech/scroll-stack-contracts
        uses: docker/build-push-action@14487ce63c7a62a4a324b0bfb37086795e31c6c1 # v6.16.0
        with:
          platforms: linux/amd64,linux/arm64
          push: true
          context: .
          file: docker/Dockerfile.gen-configs
          tags: |
            ${{ env.REPOSITORY }}:gen-configs-${{ github.sha }}
